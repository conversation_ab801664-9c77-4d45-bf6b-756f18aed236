* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 2.5em;
    color: #4a90e2;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

header p {
    font-size: 1.2em;
    color: #666;
}

.game-board {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.scoreboard {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    gap: 20px;
}

.team {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    color: white;
    flex: 1;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.team-1 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.team-2 {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.team h2 {
    font-size: 1.5em;
    margin-bottom: 10px;
}

.score {
    font-size: 1.3em;
    font-weight: bold;
}

.current-turn {
    text-align: center;
    margin-bottom: 30px;
    padding: 15px;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.current-turn h3 {
    font-size: 1.4em;
    color: #333;
}

.teams-professions {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.team-professions {
    flex: 1;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    border: 3px solid #e9ecef;
}

.team-professions h3 {
    color: #4a90e2;
    margin-bottom: 15px;
    text-align: center;
}

.professions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.profession-item {
    background: white;
    padding: 12px;
    border-radius: 10px;
    border: 2px solid #dee2e6;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.profession-item.matched {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.cards-grid-section {
    text-align: center;
    margin-bottom: 30px;
}

.cards-grid-section h3 {
    color: #4a90e2;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.cards-count {
    font-size: 1.1em;
    margin-bottom: 20px;
    color: #666;
    font-weight: bold;
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 3px solid #e9ecef;
}

.card-item {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    font-size: 1.2em;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.card-item.face-down {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 3px solid #4a90e2;
}

.card-item.face-down:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.card-item.face-down:active {
    transform: scale(0.95);
}

.card-item.face-up {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #333;
    border: 3px solid #28a745;
    font-size: 2.5em;
    cursor: default;
}

.card-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #6c757d;
    color: #fff;
}

.card-item.disabled:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.selected-card-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.selected-card-display {
    margin-bottom: 20px;
}

.selected-card-number {
    font-size: 1.2em;
    font-weight: bold;
    color: #4a90e2;
    margin-bottom: 15px;
}

.selected-card {
    font-size: 4em;
    margin: 20px 0;
    padding: 20px;
    background: white;
    border-radius: 15px;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    min-width: 120px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #4a90e2;
}

.match-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.btn-success {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #333;
}

.btn-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: #333;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.matches-section {
    margin-bottom: 30px;
}

.matches-section h3 {
    color: #4a90e2;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5em;
}

.matches-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.match-item {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    padding: 15px;
    border-radius: 15px;
    text-align: center;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.match-item .emoji {
    font-size: 2em;
    display: block;
    margin-bottom: 10px;
}

.match-item .profession {
    font-weight: bold;
    color: #155724;
}

.controls {
    text-align: center;
    margin-bottom: 20px;
}

.controls .btn {
    margin: 0 10px;
}

.message {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    margin-top: 20px;
    font-weight: bold;
    font-size: 1.1em;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 2px solid #28a745;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #dc3545;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 2px solid #17a2b8;
}

/* Animații */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.bounce {
    animation: bounce 0.6s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s;
}

/* Responsive */
@media (max-width: 768px) {
    .teams-professions {
        flex-direction: column;
    }

    .scoreboard {
        flex-direction: column;
    }

    .match-buttons {
        flex-direction: column;
        align-items: center;
    }

    .controls .btn {
        display: block;
        margin: 10px auto;
        width: 200px;
    }

    .cards-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 300px;
        gap: 10px;
    }

    .card-item {
        width: 60px;
        height: 60px;
        font-size: 1em;
    }

    .card-item.face-up {
        font-size: 2em;
    }
}

@media (max-width: 480px) {
    .cards-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 200px;
    }
}
