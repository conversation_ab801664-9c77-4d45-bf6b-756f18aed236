// Datele jocului
const professions = [
    { name: "Doctor", emoji: "👨‍⚕️" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🏫" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🚒" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", emoji: "👮‍♂️" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", emoji: "👨‍🍳" },
    { name: "<PERSON>", emoji: "👨‍✈️" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🌾" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🔧" },
    { name: "Artist", emoji: "👨‍🎨" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🎤" }
];

// Starea jocului
let gameState = {
    currentTeam: 1,
    team1Score: 0,
    team2Score: 0,
    team1Professions: [],
    team2Professions: [],
    availableCards: [],
    drawnCard: null,
    matches: [],
    gameStarted: false
};

// Elemente DOM
const elements = {
    startBtn: document.getElementById('start-game'),
    resetBtn: document.getElementById('reset-game'),
    envelope: document.getElementById('envelope'),
    drawnCardSection: document.getElementById('drawn-card-section'),
    drawnCard: document.getElementById('drawn-card'),
    matchBtn: document.getElementById('match-btn'),
    noMatchBtn: document.getElementById('no-match-btn'),
    score1: document.getElementById('score1'),
    score2: document.getElementById('score2'),
    turnIndicator: document.getElementById('turn-indicator'),
    team1Professions: document.getElementById('team1-professions'),
    team2Professions: document.getElementById('team2-professions'),
    cardsLeft: document.getElementById('cards-left'),
    matchesContainer: document.getElementById('matches-container'),
    message: document.getElementById('message')
};

// Funcții pentru sunete
function playSuccessSound() {
    // Creăm un sunet de succes folosind Web Audio API
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
    oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
    oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
}

function playErrorSound() {
    // Creăm un sunet de eroare
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(150, audioContext.currentTime + 0.1);
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
}

// Funcții utilitare
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

function showMessage(text, type = 'info') {
    elements.message.textContent = text;
    elements.message.className = `message ${type}`;
    setTimeout(() => {
        elements.message.textContent = '';
        elements.message.className = 'message';
    }, 3000);
}

// Funcții de joc
function initializeGame() {
    // Amestecăm meseriile și le împărțim în două echipe
    const shuffledProfessions = shuffleArray(professions);
    gameState.team1Professions = shuffledProfessions.slice(0, 5);
    gameState.team2Professions = shuffledProfessions.slice(5, 10);
    
    // Inițializăm cărțile disponibile
    gameState.availableCards = [...professions];
    
    // Resetăm starea
    gameState.currentTeam = 1;
    gameState.team1Score = 0;
    gameState.team2Score = 0;
    gameState.matches = [];
    gameState.drawnCard = null;
    gameState.gameStarted = true;
    
    updateDisplay();
    showMessage('Jocul a început! Echipa 1 începe!', 'success');
}

function updateDisplay() {
    // Actualizăm scorul
    elements.score1.textContent = gameState.team1Score;
    elements.score2.textContent = gameState.team2Score;
    
    // Actualizăm indicatorul de rând
    elements.turnIndicator.textContent = `Rândul echipei ${gameState.currentTeam}`;
    
    // Actualizăm meseriile echipelor
    updateTeamProfessions();
    
    // Actualizăm numărul de cărți rămase
    elements.cardsLeft.textContent = gameState.availableCards.length;
    
    // Actualizăm potrivirile
    updateMatches();
    
    // Ascundem secțiunea de carte trasă dacă nu avem carte
    if (!gameState.drawnCard) {
        elements.drawnCardSection.style.display = 'none';
    }
}

function updateTeamProfessions() {
    // Echipa 1
    elements.team1Professions.innerHTML = '';
    gameState.team1Professions.forEach(profession => {
        const div = document.createElement('div');
        div.className = 'profession-item';
        div.textContent = profession.name;
        
        // Verificăm dacă meseria a fost potrivită
        const isMatched = gameState.matches.some(match => match.name === profession.name);
        if (isMatched) {
            div.classList.add('matched');
        }
        
        elements.team1Professions.appendChild(div);
    });
    
    // Echipa 2
    elements.team2Professions.innerHTML = '';
    gameState.team2Professions.forEach(profession => {
        const div = document.createElement('div');
        div.className = 'profession-item';
        div.textContent = profession.name;
        
        // Verificăm dacă meseria a fost potrivită
        const isMatched = gameState.matches.some(match => match.name === profession.name);
        if (isMatched) {
            div.classList.add('matched');
        }
        
        elements.team2Professions.appendChild(div);
    });
}

function updateMatches() {
    elements.matchesContainer.innerHTML = '';
    gameState.matches.forEach(match => {
        const div = document.createElement('div');
        div.className = 'match-item';
        div.innerHTML = `
            <span class="emoji">${match.emoji}</span>
            <span class="profession">${match.name}</span>
        `;
        elements.matchesContainer.appendChild(div);
    });
}

function drawCard() {
    if (!gameState.gameStarted) {
        showMessage('Începe mai întâi jocul!', 'error');
        return;
    }
    
    if (gameState.availableCards.length === 0) {
        showMessage('Nu mai sunt cărți în plic!', 'error');
        return;
    }
    
    if (gameState.drawnCard) {
        showMessage('Ai deja o carte trasă! Alege dacă se potrivește sau nu.', 'error');
        return;
    }
    
    // Tragem o carte aleatorie
    const randomIndex = Math.floor(Math.random() * gameState.availableCards.length);
    gameState.drawnCard = gameState.availableCards[randomIndex];
    
    // Afișăm carta
    elements.drawnCard.textContent = gameState.drawnCard.emoji;
    elements.drawnCardSection.style.display = 'block';
    
    // Animație pentru plic
    elements.envelope.classList.add('bounce');
    setTimeout(() => elements.envelope.classList.remove('bounce'), 600);
    
    updateDisplay();
}

function handleMatch() {
    if (!gameState.drawnCard) return;
    
    const currentTeamProfessions = gameState.currentTeam === 1 ? 
        gameState.team1Professions : gameState.team2Professions;
    
    // Verificăm dacă carta se potrivește cu o meserie a echipei curente
    const matchingProfession = currentTeamProfessions.find(p => p.name === gameState.drawnCard.name);
    
    if (matchingProfession) {
        // Potrivire corectă!
        playSuccessSound();
        
        if (gameState.currentTeam === 1) {
            gameState.team1Score += 100;
        } else {
            gameState.team2Score += 100;
        }
        
        // Adăugăm potrivirea
        gameState.matches.push(gameState.drawnCard);
        
        // Eliminăm carta din plic
        gameState.availableCards = gameState.availableCards.filter(card => card.name !== gameState.drawnCard.name);
        
        showMessage(`Bravo! Potrivire corectă! +100 puncte pentru echipa ${gameState.currentTeam}!`, 'success');
        
        // Animație de succes
        elements.drawnCard.classList.add('bounce');
        setTimeout(() => elements.drawnCard.classList.remove('bounce'), 600);
    } else {
        // Potrivire incorectă
        playErrorSound();
        
        if (gameState.currentTeam === 1) {
            gameState.team1Score += 50;
        } else {
            gameState.team2Score += 50;
        }
        
        showMessage(`Potrivire incorectă! +50 puncte pentru echipa ${gameState.currentTeam}. Carta se întoarce în plic.`, 'error');
        
        // Animație de eroare
        elements.drawnCard.classList.add('shake');
        setTimeout(() => elements.drawnCard.classList.remove('shake'), 500);
    }
    
    // Resetăm carta trasă
    gameState.drawnCard = null;
    
    // Schimbăm echipa
    gameState.currentTeam = gameState.currentTeam === 1 ? 2 : 1;
    
    updateDisplay();
    checkGameEnd();
}

function handleNoMatch() {
    if (!gameState.drawnCard) return;
    
    playErrorSound();
    
    // Echipa primește 50 de puncte
    if (gameState.currentTeam === 1) {
        gameState.team1Score += 50;
    } else {
        gameState.team2Score += 50;
    }
    
    showMessage(`Carta se întoarce în plic! +50 puncte pentru echipa ${gameState.currentTeam}!`, 'info');
    
    // Animație
    elements.drawnCard.classList.add('shake');
    setTimeout(() => elements.drawnCard.classList.remove('shake'), 500);
    
    // Resetăm carta trasă
    gameState.drawnCard = null;
    
    // Schimbăm echipa
    gameState.currentTeam = gameState.currentTeam === 1 ? 2 : 1;
    
    updateDisplay();
    checkGameEnd();
}

function checkGameEnd() {
    if (gameState.availableCards.length === 0 || gameState.matches.length === 10) {
        // Jocul s-a terminat
        gameState.gameStarted = false;
        
        let winner;
        if (gameState.team1Score > gameState.team2Score) {
            winner = "Echipa 1";
        } else if (gameState.team2Score > gameState.team1Score) {
            winner = "Echipa 2";
        } else {
            winner = "Egalitate";
        }
        
        setTimeout(() => {
            showMessage(`🎉 Jocul s-a terminat! Câștigător: ${winner}! 🎉`, 'success');
        }, 1000);
    }
}

function resetGame() {
    gameState = {
        currentTeam: 1,
        team1Score: 0,
        team2Score: 0,
        team1Professions: [],
        team2Professions: [],
        availableCards: [],
        drawnCard: null,
        matches: [],
        gameStarted: false
    };
    
    updateDisplay();
    showMessage('Jocul a fost resetat!', 'info');
}

// Event listeners
elements.startBtn.addEventListener('click', initializeGame);
elements.resetBtn.addEventListener('click', resetGame);
elements.envelope.addEventListener('click', drawCard);
elements.matchBtn.addEventListener('click', handleMatch);
elements.noMatchBtn.addEventListener('click', handleNoMatch);

// Inițializare
updateDisplay();
